class AnalysisResult {
  final int? id;
  final int? workoutSessionId;
  final int correctSquats;
  final int incorrectSquats;
  final double avgKneeAngle;
  final double avgHipAngle;
  final List<String> feedback;
  final DateTime? createdAt;

  AnalysisResult({
    this.id,
    this.workoutSessionId,
    required this.correctSquats,
    required this.incorrectSquats,
    required this.avgKneeAngle,
    required this.avgHipAngle,
    required this.feedback,
    this.createdAt,
  });

  // Convert AnalysisResult object to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workout_session_id': workoutSessionId,
      'correct_squats': correctSquats,
      'incorrect_squats': incorrectSquats,
      'avg_knee_angle': avgKneeAngle,
      'avg_hip_angle': avgHipAngle,
      'feedback': feedback.join('|'), // Store as pipe-separated string
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Create AnalysisResult object from Map (database result)
  factory AnalysisResult.fromMap(Map<String, dynamic> map) {
    return AnalysisResult(
      id: map['id'],
      workoutSessionId: map['workout_session_id'],
      correctSquats: map['correct_squats'],
      incorrectSquats: map['incorrect_squats'],
      avgKneeAngle: map['avg_knee_angle'],
      avgHipAngle: map['avg_hip_angle'],
      feedback: map['feedback'] != null
          ? (map['feedback'] as String).split('|')
          : <String>[],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : null,
    );
  }

  factory AnalysisResult.fromJson(Map<String, dynamic> json) {
    return AnalysisResult(
      correctSquats: json['correctSquats'],
      incorrectSquats: json['incorrectSquats'],
      avgKneeAngle: json['avgKneeAngle'],
      avgHipAngle: json['avgHipAngle'],
      feedback: List<String>.from(json['feedback']),
    );
  }

  // Create a copy of AnalysisResult with updated fields
  AnalysisResult copyWith({
    int? id,
    int? workoutSessionId,
    int? correctSquats,
    int? incorrectSquats,
    double? avgKneeAngle,
    double? avgHipAngle,
    List<String>? feedback,
    DateTime? createdAt,
  }) {
    return AnalysisResult(
      id: id ?? this.id,
      workoutSessionId: workoutSessionId ?? this.workoutSessionId,
      correctSquats: correctSquats ?? this.correctSquats,
      incorrectSquats: incorrectSquats ?? this.incorrectSquats,
      avgKneeAngle: avgKneeAngle ?? this.avgKneeAngle,
      avgHipAngle: avgHipAngle ?? this.avgHipAngle,
      feedback: feedback ?? this.feedback,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AnalysisResult{id: $id, correctSquats: $correctSquats, incorrectSquats: $incorrectSquats, avgKneeAngle: $avgKneeAngle, avgHipAngle: $avgHipAngle}';
  }
}
