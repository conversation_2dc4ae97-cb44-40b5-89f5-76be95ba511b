class AnalysisResult {
  final int correctSquats;
  final int incorrectSquats;
  final double avgKneeAngle;
  final double avgHipAngle;
  final List<String> feedback;

  AnalysisResult({
    required this.correctSquats,
    required this.incorrectSquats,
    required this.avgKneeAngle,
    required this.avgHipAngle,
    required this.feedback,
  });

  factory AnalysisResult.fromJson(Map<String, dynamic> json) {
    return AnalysisResult(
      correctSquats: json['correctSquats'],
      incorrectSquats: json['incorrectSquats'],
      avgKneeAngle: json['avgKneeAngle'],
      avgHipAngle: json['avgHipAngle'],
      feedback: List<String>.from(json['feedback']),
    );
  }
}
