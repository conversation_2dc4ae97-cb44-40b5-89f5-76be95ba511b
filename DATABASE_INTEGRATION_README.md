# Fitness AI App - Database Integration

This document explains how to use the SQLite database integration in the Fitness AI App.

## Overview

The app now includes a complete SQLite database integration using the `sqflite` package. The database stores:

- **User profiles** (name, email, age, height, weight, fitness level)
- **Workout sessions** (exercise type, duration, reps, accuracy)
- **Analysis results** (AI analysis data from pose detection)

## Database Structure

### Tables

1. **users**
   - `id` (Primary Key)
   - `name`, `email`, `age`, `height`, `weight`, `fitness_level`
   - `created_at`, `updated_at`

2. **workout_sessions**
   - `id` (Primary Key)
   - `user_id` (Foreign Key to users)
   - `exercise_type`, `start_time`, `end_time`, `duration`
   - `total_reps`, `correct_reps`, `incorrect_reps`, `avg_accuracy`
   - `notes`, `created_at`

3. **analysis_results**
   - `id` (Primary Key)
   - `workout_session_id` (Foreign Key to workout_sessions)
   - `correct_squats`, `incorrect_squats`
   - `avg_knee_angle`, `avg_hip_angle`, `feedback`
   - `created_at`

## How to Use

### 1. In-App Database Demo

1. **Launch the app** on your Android device/emulator
2. **Navigate to Home screen** and tap the "Database Demo" button
3. **Create a demo user** by tapping "Create Demo User"
4. **Start a workout session** by tapping "Start Workout"
5. **End the workout** by tapping "End Workout"
6. **Add analysis results** by tapping "Add Analysis Result"
7. **View statistics** by tapping "Show Stats"
8. **View workout history** by tapping "Show History"

### 2. Database Viewer

1. From the Database Demo screen, tap the **storage icon** in the top-right corner
2. This opens the **Database Viewer** with three tabs:
   - **Users**: View all registered users
   - **Workouts**: View all workout sessions
   - **Analysis**: View all analysis results

### 3. Using DB Browser (SQLite)

#### Option A: Extract Database from Device/Emulator

1. **Connect your device/emulator**
2. **Use ADB to extract the database**:
   ```bash
   adb pull /data/data/com.example.fitness_ai_app/databases/fitness_app.db ./fitness_app.db
   ```
3. **Open the database file** in DB Browser (SQLite)

#### Option B: Use the Provided Schema

1. **Open DB Browser (SQLite)**
2. **Create a new database** or open an existing one
3. **Use the SQL script** from `database_schema.sql` to create the tables
4. **Import data** or create sample data manually

## Code Structure

### Models
- `lib/models/user.dart` - User data model
- `lib/models/workout_session.dart` - Workout session data model
- `lib/models/analysis_result.dart` - Analysis result data model

### Services
- `lib/services/database_helper.dart` - Low-level database operations
- `lib/services/fitness_service.dart` - High-level fitness-related database operations

### UI Components
- `lib/screens/database_demo_screen.dart` - Demo screen for testing database functionality
- `lib/widgets/shared/database_viewer.dart` - Database browser widget

## Sample Queries

Here are some useful SQL queries you can run in DB Browser:

### Get User Statistics
```sql
SELECT 
    u.name,
    COUNT(ws.id) as total_workouts,
    SUM(ws.duration) as total_time_seconds,
    AVG(ws.avg_accuracy) as average_accuracy,
    SUM(ws.correct_reps) as total_correct_reps
FROM users u
LEFT JOIN workout_sessions ws ON u.id = ws.user_id
GROUP BY u.id, u.name;
```

### Get Weekly Progress
```sql
SELECT 
    DATE(created_at) as workout_date,
    COUNT(*) as workouts_count,
    SUM(duration) as total_time,
    AVG(avg_accuracy) as avg_accuracy
FROM workout_sessions 
WHERE user_id = 1 AND created_at >= datetime('now', '-7 days')
GROUP BY DATE(created_at)
ORDER BY workout_date DESC;
```

### Get Analysis Results with Workout Details
```sql
SELECT 
    ws.exercise_type,
    ws.created_at as workout_date,
    ar.correct_squats,
    ar.incorrect_squats,
    ar.avg_knee_angle,
    ar.avg_hip_angle
FROM analysis_results ar
JOIN workout_sessions ws ON ar.workout_session_id = ws.id
ORDER BY ws.created_at DESC;
```

## Features

### ✅ Implemented
- Complete database schema with relationships
- CRUD operations for all entities
- User management (create, read, update, delete)
- Workout session tracking
- Analysis result storage
- Statistics and analytics queries
- In-app database viewer
- Demo functionality for testing

### 🔄 Future Enhancements
- Data export/import functionality
- Database backup and restore
- Data synchronization with cloud services
- Advanced analytics and reporting
- Data visualization charts

## Troubleshooting

### Database Not Found
- Make sure the app has been launched at least once to create the database
- Check if the app has proper storage permissions
- Verify the database path is correct for your device

### Permission Issues
- Use an emulator for easier database access
- Root access may be required on physical devices
- Consider using the in-app Database Viewer instead

### Performance Issues
- The database includes indexes for better performance
- Consider pagination for large datasets
- Use the provided analytics queries for efficient data retrieval

## Dependencies

- `sqflite: ^2.3.0` - SQLite database for Flutter
- `path: ^1.8.3` - Path manipulation utilities

## Database Location

- **Android**: `/data/data/com.example.fitness_ai_app/databases/fitness_app.db`
- **iOS**: `Documents/fitness_app.db` (in app sandbox)

## Support

For issues or questions about the database integration, please refer to:
- Flutter SQLite documentation: https://pub.dev/packages/sqflite
- DB Browser (SQLite): https://sqlitebrowser.org/
