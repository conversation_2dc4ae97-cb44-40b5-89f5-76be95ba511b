import 'package:flutter/material.dart';
import '../../services/database_helper.dart';
import '../../models/user.dart';
import '../../models/workout_session.dart';
import '../../models/analysis_result.dart';

class DatabaseViewer extends StatefulWidget {
  const DatabaseViewer({super.key});

  @override
  State<DatabaseViewer> createState() => _DatabaseViewerState();
}

class _DatabaseViewerState extends State<DatabaseViewer> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _dbHelper = DatabaseHelper();
  
  List<User> _users = [];
  List<WorkoutSession> _workoutSessions = [];
  List<AnalysisResult> _analysisResults = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final users = await _dbHelper.getAllUsers();
      final workoutSessions = await _dbHelper.getAllWorkoutSessions();
      
      // Get all analysis results for all workout sessions
      List<AnalysisResult> allAnalysisResults = [];
      for (final session in workoutSessions) {
        if (session.id != null) {
          final results = await _dbHelper.getAnalysisResultsByWorkoutSessionId(session.id!);
          allAnalysisResults.addAll(results);
        }
      }
      
      setState(() {
        _users = users;
        _workoutSessions = workoutSessions;
        _analysisResults = allAnalysisResults;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Viewer'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Users', icon: Icon(Icons.person)),
            Tab(text: 'Workouts', icon: Icon(Icons.fitness_center)),
            Tab(text: 'Analysis', icon: Icon(Icons.analytics)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUsersTab(),
                _buildWorkoutsTab(),
                _buildAnalysisTab(),
              ],
            ),
    );
  }

  Widget _buildUsersTab() {
    if (_users.isEmpty) {
      return const Center(
        child: Text('No users found'),
      );
    }

    return ListView.builder(
      itemCount: _users.length,
      itemBuilder: (context, index) {
        final user = _users[index];
        return Card(
          margin: const EdgeInsets.all(8.0),
          child: ListTile(
            leading: CircleAvatar(
              child: Text(user.name[0].toUpperCase()),
            ),
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Email: ${user.email}'),
                Text('Age: ${user.age}, Height: ${user.height}cm, Weight: ${user.weight}kg'),
                Text('Fitness Level: ${user.fitnessLevel}'),
                Text('Created: ${user.createdAt.toString().split('.')[0]}'),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget _buildWorkoutsTab() {
    if (_workoutSessions.isEmpty) {
      return const Center(
        child: Text('No workout sessions found'),
      );
    }

    return ListView.builder(
      itemCount: _workoutSessions.length,
      itemBuilder: (context, index) {
        final session = _workoutSessions[index];
        return Card(
          margin: const EdgeInsets.all(8.0),
          child: ListTile(
            leading: Icon(
              Icons.fitness_center,
              color: session.avgAccuracy >= 80 ? Colors.green : 
                     session.avgAccuracy >= 60 ? Colors.orange : Colors.red,
            ),
            title: Text('${session.exerciseType} - User ID: ${session.userId}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Duration: ${session.duration}s'),
                Text('Reps: ${session.correctReps}/${session.totalReps} (${session.avgAccuracy.toStringAsFixed(1)}%)'),
                Text('Started: ${session.startTime.toString().split('.')[0]}'),
                if (session.notes != null) Text('Notes: ${session.notes}'),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget _buildAnalysisTab() {
    if (_analysisResults.isEmpty) {
      return const Center(
        child: Text('No analysis results found'),
      );
    }

    return ListView.builder(
      itemCount: _analysisResults.length,
      itemBuilder: (context, index) {
        final result = _analysisResults[index];
        return Card(
          margin: const EdgeInsets.all(8.0),
          child: ListTile(
            leading: Icon(
              Icons.analytics,
              color: result.correctSquats > result.incorrectSquats ? Colors.green : Colors.orange,
            ),
            title: Text('Session ID: ${result.workoutSessionId}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Squats: ${result.correctSquats} correct, ${result.incorrectSquats} incorrect'),
                Text('Avg Knee Angle: ${result.avgKneeAngle.toStringAsFixed(1)}°'),
                Text('Avg Hip Angle: ${result.avgHipAngle.toStringAsFixed(1)}°'),
                if (result.feedback.isNotEmpty) 
                  Text('Feedback: ${result.feedback.join(', ')}'),
                if (result.createdAt != null)
                  Text('Created: ${result.createdAt.toString().split('.')[0]}'),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }
}
