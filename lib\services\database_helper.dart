import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/workout_session.dart';
import '../models/analysis_result.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'fitness_app.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        age INTEGER NOT NULL,
        height REAL NOT NULL,
        weight REAL NOT NULL,
        fitness_level TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create workout_sessions table
    await db.execute('''
      CREATE TABLE workout_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        exercise_type TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT,
        duration INTEGER NOT NULL,
        total_reps INTEGER NOT NULL,
        correct_reps INTEGER NOT NULL,
        incorrect_reps INTEGER NOT NULL,
        avg_accuracy REAL NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create analysis_results table
    await db.execute('''
      CREATE TABLE analysis_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workout_session_id INTEGER NOT NULL,
        correct_squats INTEGER NOT NULL,
        incorrect_squats INTEGER NOT NULL,
        avg_knee_angle REAL NOT NULL,
        avg_hip_angle REAL NOT NULL,
        feedback TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (workout_session_id) REFERENCES workout_sessions (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_workout_sessions_user_id ON workout_sessions (user_id)');
    await db.execute('CREATE INDEX idx_workout_sessions_created_at ON workout_sessions (created_at)');
    await db.execute('CREATE INDEX idx_analysis_results_workout_session_id ON analysis_results (workout_session_id)');
  }

  // User CRUD operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<User?> getUserById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // WorkoutSession CRUD operations
  Future<int> insertWorkoutSession(WorkoutSession session) async {
    final db = await database;
    return await db.insert('workout_sessions', session.toMap());
  }

  Future<WorkoutSession?> getWorkoutSessionById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_sessions',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return WorkoutSession.fromMap(maps.first);
    }
    return null;
  }

  Future<List<WorkoutSession>> getWorkoutSessionsByUserId(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_sessions',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkoutSession.fromMap(maps[i]));
  }

  Future<List<WorkoutSession>> getAllWorkoutSessions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_sessions',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => WorkoutSession.fromMap(maps[i]));
  }

  Future<int> updateWorkoutSession(WorkoutSession session) async {
    final db = await database;
    return await db.update(
      'workout_sessions',
      session.toMap(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  Future<int> deleteWorkoutSession(int id) async {
    final db = await database;
    return await db.delete(
      'workout_sessions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // AnalysisResult CRUD operations
  Future<int> insertAnalysisResult(AnalysisResult result) async {
    final db = await database;
    return await db.insert('analysis_results', result.toMap());
  }

  Future<AnalysisResult?> getAnalysisResultById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'analysis_results',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return AnalysisResult.fromMap(maps.first);
    }
    return null;
  }

  Future<List<AnalysisResult>> getAnalysisResultsByWorkoutSessionId(int workoutSessionId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'analysis_results',
      where: 'workout_session_id = ?',
      whereArgs: [workoutSessionId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => AnalysisResult.fromMap(maps[i]));
  }

  Future<int> updateAnalysisResult(AnalysisResult result) async {
    final db = await database;
    return await db.update(
      'analysis_results',
      result.toMap(),
      where: 'id = ?',
      whereArgs: [result.id],
    );
  }

  Future<int> deleteAnalysisResult(int id) async {
    final db = await database;
    return await db.delete(
      'analysis_results',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Statistics and Analytics methods
  Future<Map<String, dynamic>> getUserStats(int userId) async {
    final db = await database;

    // Get total workouts
    final totalWorkoutsResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM workout_sessions WHERE user_id = ?',
      [userId],
    );
    final totalWorkouts = totalWorkoutsResult.first['count'] as int;

    // Get total exercise time
    final totalTimeResult = await db.rawQuery(
      'SELECT SUM(duration) as total_time FROM workout_sessions WHERE user_id = ?',
      [userId],
    );
    final totalTime = (totalTimeResult.first['total_time'] as int?) ?? 0;

    // Get average accuracy
    final avgAccuracyResult = await db.rawQuery(
      'SELECT AVG(avg_accuracy) as avg_accuracy FROM workout_sessions WHERE user_id = ?',
      [userId],
    );
    final avgAccuracy = (avgAccuracyResult.first['avg_accuracy'] as double?) ?? 0.0;

    // Get total correct reps
    final totalCorrectRepsResult = await db.rawQuery(
      'SELECT SUM(correct_reps) as total_correct_reps FROM workout_sessions WHERE user_id = ?',
      [userId],
    );
    final totalCorrectReps = (totalCorrectRepsResult.first['total_correct_reps'] as int?) ?? 0;

    return {
      'totalWorkouts': totalWorkouts,
      'totalTime': totalTime,
      'avgAccuracy': avgAccuracy,
      'totalCorrectReps': totalCorrectReps,
    };
  }

  Future<List<Map<String, dynamic>>> getWeeklyProgress(int userId) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT
        DATE(created_at) as date,
        COUNT(*) as workouts,
        SUM(duration) as total_time,
        AVG(avg_accuracy) as avg_accuracy,
        SUM(correct_reps) as correct_reps
      FROM workout_sessions
      WHERE user_id = ? AND created_at >= datetime('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    ''', [userId]);

    return result;
  }

  Future<List<Map<String, dynamic>>> getExerciseTypeStats(int userId) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT
        exercise_type,
        COUNT(*) as count,
        SUM(duration) as total_time,
        AVG(avg_accuracy) as avg_accuracy,
        SUM(correct_reps) as total_correct_reps
      FROM workout_sessions
      WHERE user_id = ?
      GROUP BY exercise_type
      ORDER BY count DESC
    ''', [userId]);

    return result;
  }

  // Utility methods
  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'fitness_app.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
