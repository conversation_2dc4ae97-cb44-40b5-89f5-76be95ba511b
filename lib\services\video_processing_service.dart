import 'dart:io';
import 'package:ffmpeg_kit_flutter_full_gpl/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_full_gpl/return_code.dart';
import 'package:path_provider/path_provider.dart';

class VideoProcessingService {
  Future<String?> generateVideo(List<String> framePaths, String videoPath) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String rawVideoPath = '${appDir.path}/raw_video.mp4';

    // Create a temporary file with the list of frames
    final File file = File('${appDir.path}/frames.txt');
    await file.writeAsString(framePaths.map((path) => "file '$path'").join('\n'));

    // Use FFmpeg to create a video from the frames
    final String command =
        '-r 30 -f concat -safe 0 -i ${file.path} -c:v libx264 -pix_fmt yuv420p $rawVideoPath';

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      // Add the audio from the original video to the new video
      final String finalVideoPath = '${appDir.path}/final_video.mp4';
      final String audioCommand =
          '-i $rawVideoPath -i $videoPath -c:v copy -c:a aac -strict experimental $finalVideoPath';

      final audioSession = await FFmpegKit.execute(audioCommand);
      final audioReturnCode = await audioSession.getReturnCode();

      if (ReturnCode.isSuccess(audioReturnCode)) {
        return finalVideoPath;
      }
    }

    return null;
  }
}
