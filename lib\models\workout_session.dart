class WorkoutSession {
  final int? id;
  final int userId;
  final String exerciseType; // squat, pushup, etc.
  final DateTime startTime;
  final DateTime? endTime;
  final int duration; // in seconds
  final int totalReps;
  final int correctReps;
  final int incorrectReps;
  final double avgAccuracy; // percentage
  final String? notes;
  final DateTime createdAt;

  WorkoutSession({
    this.id,
    required this.userId,
    required this.exerciseType,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.totalReps,
    required this.correctReps,
    required this.incorrectReps,
    required this.avgAccuracy,
    this.notes,
    required this.createdAt,
  });

  // Convert WorkoutSession object to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'exercise_type': exerciseType,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration': duration,
      'total_reps': totalReps,
      'correct_reps': correctReps,
      'incorrect_reps': incorrectReps,
      'avg_accuracy': avgAccuracy,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Create WorkoutSession object from Map (database result)
  factory WorkoutSession.fromMap(Map<String, dynamic> map) {
    return WorkoutSession(
      id: map['id'],
      userId: map['user_id'],
      exerciseType: map['exercise_type'],
      startTime: DateTime.parse(map['start_time']),
      endTime: map['end_time'] != null ? DateTime.parse(map['end_time']) : null,
      duration: map['duration'],
      totalReps: map['total_reps'],
      correctReps: map['correct_reps'],
      incorrectReps: map['incorrect_reps'],
      avgAccuracy: map['avg_accuracy'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // Create a copy of WorkoutSession with updated fields
  WorkoutSession copyWith({
    int? id,
    int? userId,
    String? exerciseType,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    int? totalReps,
    int? correctReps,
    int? incorrectReps,
    double? avgAccuracy,
    String? notes,
    DateTime? createdAt,
  }) {
    return WorkoutSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      exerciseType: exerciseType ?? this.exerciseType,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      totalReps: totalReps ?? this.totalReps,
      correctReps: correctReps ?? this.correctReps,
      incorrectReps: incorrectReps ?? this.incorrectReps,
      avgAccuracy: avgAccuracy ?? this.avgAccuracy,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'WorkoutSession{id: $id, userId: $userId, exerciseType: $exerciseType, totalReps: $totalReps, correctReps: $correctReps, avgAccuracy: $avgAccuracy}';
  }
}
