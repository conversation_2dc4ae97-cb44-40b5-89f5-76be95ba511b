import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/workout_session.dart';
import '../models/analysis_result.dart';
import 'database_helper.dart';

class FitnessService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // User management
  Future<User?> createUser({
    required String name,
    required String email,
    required int age,
    required double height,
    required double weight,
    required String fitnessLevel,
  }) async {
    try {
      // Check if user already exists
      final existingUser = await _dbHelper.getUserByEmail(email);
      if (existingUser != null) {
        debugPrint('User with email $email already exists');
        return null;
      }

      final now = DateTime.now();
      final user = User(
        name: name,
        email: email,
        age: age,
        height: height,
        weight: weight,
        fitnessLevel: fitnessLevel,
        createdAt: now,
        updatedAt: now,
      );

      final userId = await _dbHelper.insertUser(user);
      return user.copyWith(id: userId);
    } catch (e) {
      debugPrint('Error creating user: $e');
      return null;
    }
  }

  Future<User?> getUserByEmail(String email) async {
    try {
      return await _dbHelper.getUserByEmail(email);
    } catch (e) {
      debugPrint('Error getting user by email: $e');
      return null;
    }
  }

  Future<User?> updateUser(User user) async {
    try {
      final updatedUser = user.copyWith(updatedAt: DateTime.now());
      await _dbHelper.updateUser(updatedUser);
      return updatedUser;
    } catch (e) {
      debugPrint('Error updating user: $e');
      return null;
    }
  }

  // Workout session management
  Future<WorkoutSession?> startWorkoutSession({
    required int userId,
    required String exerciseType,
  }) async {
    try {
      final now = DateTime.now();
      final session = WorkoutSession(
        userId: userId,
        exerciseType: exerciseType,
        startTime: now,
        duration: 0,
        totalReps: 0,
        correctReps: 0,
        incorrectReps: 0,
        avgAccuracy: 0.0,
        createdAt: now,
      );

      final sessionId = await _dbHelper.insertWorkoutSession(session);
      return session.copyWith(id: sessionId);
    } catch (e) {
      debugPrint('Error starting workout session: $e');
      return null;
    }
  }

  Future<WorkoutSession?> endWorkoutSession({
    required int sessionId,
    required int totalReps,
    required int correctReps,
    required int incorrectReps,
    String? notes,
  }) async {
    try {
      final session = await _dbHelper.getWorkoutSessionById(sessionId);
      if (session == null) {
        debugPrint('Workout session not found');
        return null;
      }

      final now = DateTime.now();
      final duration = now.difference(session.startTime).inSeconds;
      final accuracy = totalReps > 0 ? (correctReps / totalReps) * 100 : 0.0;

      final updatedSession = session.copyWith(
        endTime: now,
        duration: duration,
        totalReps: totalReps,
        correctReps: correctReps,
        incorrectReps: incorrectReps,
        avgAccuracy: accuracy,
        notes: notes,
      );

      await _dbHelper.updateWorkoutSession(updatedSession);
      return updatedSession;
    } catch (e) {
      debugPrint('Error ending workout session: $e');
      return null;
    }
  }

  Future<List<WorkoutSession>> getUserWorkoutHistory(int userId) async {
    try {
      return await _dbHelper.getWorkoutSessionsByUserId(userId);
    } catch (e) {
      debugPrint('Error getting user workout history: $e');
      return [];
    }
  }

  // Analysis result management
  Future<AnalysisResult?> saveAnalysisResult({
    required int workoutSessionId,
    required int correctSquats,
    required int incorrectSquats,
    required double avgKneeAngle,
    required double avgHipAngle,
    required List<String> feedback,
  }) async {
    try {
      final result = AnalysisResult(
        workoutSessionId: workoutSessionId,
        correctSquats: correctSquats,
        incorrectSquats: incorrectSquats,
        avgKneeAngle: avgKneeAngle,
        avgHipAngle: avgHipAngle,
        feedback: feedback,
        createdAt: DateTime.now(),
      );

      final resultId = await _dbHelper.insertAnalysisResult(result);
      return result.copyWith(id: resultId);
    } catch (e) {
      debugPrint('Error saving analysis result: $e');
      return null;
    }
  }

  Future<List<AnalysisResult>> getSessionAnalysisResults(int workoutSessionId) async {
    try {
      return await _dbHelper.getAnalysisResultsByWorkoutSessionId(workoutSessionId);
    } catch (e) {
      debugPrint('Error getting session analysis results: $e');
      return [];
    }
  }

  // Statistics and analytics
  Future<Map<String, dynamic>> getUserStats(int userId) async {
    try {
      return await _dbHelper.getUserStats(userId);
    } catch (e) {
      debugPrint('Error getting user stats: $e');
      return {
        'totalWorkouts': 0,
        'totalTime': 0,
        'avgAccuracy': 0.0,
        'totalCorrectReps': 0,
      };
    }
  }

  Future<List<Map<String, dynamic>>> getWeeklyProgress(int userId) async {
    try {
      return await _dbHelper.getWeeklyProgress(userId);
    } catch (e) {
      debugPrint('Error getting weekly progress: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getExerciseTypeStats(int userId) async {
    try {
      return await _dbHelper.getExerciseTypeStats(userId);
    } catch (e) {
      debugPrint('Error getting exercise type stats: $e');
      return [];
    }
  }

  // Utility methods
  Future<void> deleteAllUserData(int userId) async {
    try {
      await _dbHelper.deleteUser(userId);
      debugPrint('All user data deleted successfully');
    } catch (e) {
      debugPrint('Error deleting user data: $e');
    }
  }

  Future<void> exportUserData(int userId) async {
    try {
      final user = await _dbHelper.getUserById(userId);
      final workouts = await _dbHelper.getWorkoutSessionsByUserId(userId);
      final stats = await _dbHelper.getUserStats(userId);
      
      debugPrint('User Data Export:');
      debugPrint('User: $user');
      debugPrint('Total Workouts: ${workouts.length}');
      debugPrint('Stats: $stats');
      
      // Here you could implement actual export functionality
      // like saving to a file or sending to a server
    } catch (e) {
      debugPrint('Error exporting user data: $e');
    }
  }
}
