# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Web related
/web/build/

# Windows, macOS, Linux build outputs
/windows/build/
/macos/build/
/linux/build/

# Environment and sensitive files
.env
.env.local
.env.production
.env.staging
*.pem
*.p12
*.jks
*.keystore
google-services.json
GoogleService-Info.plist

# IDE specific
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json

# Local configuration (already present but ensuring it's covered)
android/local.properties
ios/Runner/GoogleService-Info.plist
android/app/google-services.json

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Coverage
coverage/
lcov.info

# Firebase
.firebase/
firebase_options.dart

# Temporary files
*.tmp
*.temp
