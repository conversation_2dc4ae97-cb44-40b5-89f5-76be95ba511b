-- Fitness AI App Database Schema
-- This file contains the database structure for the Flutter fitness app
-- You can use this with <PERSON> Browser (SQLite) to understand the database structure

-- Users table - stores user profile information
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    age INTEGER NOT NULL,
    height REAL NOT NULL,           -- Height in centimeters
    weight REAL NOT NULL,           -- Weight in kilograms
    fitness_level TEXT NOT NULL,    -- 'beginner', 'intermediate', 'advanced'
    created_at TEXT NOT NULL,       -- ISO 8601 datetime string
    updated_at TEXT NOT NULL        -- ISO 8601 datetime string
);

-- Workout sessions table - stores individual workout sessions
CREATE TABLE workout_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    exercise_type TEXT NOT NULL,    -- 'squat', 'pushup', etc.
    start_time TEXT NOT NULL,       -- ISO 8601 datetime string
    end_time TEXT,                  -- ISO 8601 datetime string (nullable)
    duration INTEGER NOT NULL,      -- Duration in seconds
    total_reps INTEGER NOT NULL,    -- Total repetitions performed
    correct_reps INTEGER NOT NULL,  -- Correctly performed repetitions
    incorrect_reps INTEGER NOT NULL,-- Incorrectly performed repetitions
    avg_accuracy REAL NOT NULL,     -- Average accuracy percentage
    notes TEXT,                     -- Optional notes (nullable)
    created_at TEXT NOT NULL,       -- ISO 8601 datetime string
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Analysis results table - stores AI analysis results for workout sessions
CREATE TABLE analysis_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workout_session_id INTEGER NOT NULL,
    correct_squats INTEGER NOT NULL,    -- Number of correct squats detected
    incorrect_squats INTEGER NOT NULL,  -- Number of incorrect squats detected
    avg_knee_angle REAL NOT NULL,       -- Average knee angle in degrees
    avg_hip_angle REAL NOT NULL,        -- Average hip angle in degrees
    feedback TEXT,                      -- Pipe-separated feedback strings
    created_at TEXT NOT NULL,           -- ISO 8601 datetime string
    FOREIGN KEY (workout_session_id) REFERENCES workout_sessions (id) ON DELETE CASCADE
);

-- Indexes for better query performance
CREATE INDEX idx_workout_sessions_user_id ON workout_sessions (user_id);
CREATE INDEX idx_workout_sessions_created_at ON workout_sessions (created_at);
CREATE INDEX idx_analysis_results_workout_session_id ON analysis_results (workout_session_id);

-- Sample queries you can run in DB Browser (SQLite):

-- 1. Get all users with their basic info
-- SELECT * FROM users ORDER BY created_at DESC;

-- 2. Get workout sessions for a specific user
-- SELECT * FROM workout_sessions WHERE user_id = 1 ORDER BY created_at DESC;

-- 3. Get user statistics (total workouts, average accuracy, etc.)
-- SELECT 
--     u.name,
--     COUNT(ws.id) as total_workouts,
--     SUM(ws.duration) as total_time_seconds,
--     AVG(ws.avg_accuracy) as average_accuracy,
--     SUM(ws.correct_reps) as total_correct_reps
-- FROM users u
-- LEFT JOIN workout_sessions ws ON u.id = ws.user_id
-- GROUP BY u.id, u.name;

-- 4. Get analysis results with workout session details
-- SELECT 
--     ws.exercise_type,
--     ws.created_at as workout_date,
--     ar.correct_squats,
--     ar.incorrect_squats,
--     ar.avg_knee_angle,
--     ar.avg_hip_angle,
--     ar.feedback
-- FROM analysis_results ar
-- JOIN workout_sessions ws ON ar.workout_session_id = ws.id
-- ORDER BY ws.created_at DESC;

-- 5. Get weekly progress for a user
-- SELECT 
--     DATE(created_at) as workout_date,
--     COUNT(*) as workouts_count,
--     SUM(duration) as total_time,
--     AVG(avg_accuracy) as avg_accuracy,
--     SUM(correct_reps) as correct_reps
-- FROM workout_sessions 
-- WHERE user_id = 1 AND created_at >= datetime('now', '-7 days')
-- GROUP BY DATE(created_at)
-- ORDER BY workout_date DESC;

-- 6. Get exercise type statistics
-- SELECT 
--     exercise_type,
--     COUNT(*) as session_count,
--     SUM(duration) as total_time,
--     AVG(avg_accuracy) as avg_accuracy,
--     SUM(correct_reps) as total_correct_reps
-- FROM workout_sessions 
-- WHERE user_id = 1
-- GROUP BY exercise_type
-- ORDER BY session_count DESC;

-- Database location on Android:
-- /data/data/com.example.fitness_ai_app/databases/fitness_app.db
-- 
-- Note: You'll need root access or use an emulator to access the database file directly.
-- Alternatively, you can use the built-in Database Viewer in the app to browse the data.

-- To export data from the app database:
-- 1. Use the Database Viewer widget in the app
-- 2. Or implement export functionality in the FitnessService
-- 3. Or use adb to pull the database file from the device/emulator:
--    adb pull /data/data/com.example.fitness_ai_app/databases/fitness_app.db ./fitness_app.db
