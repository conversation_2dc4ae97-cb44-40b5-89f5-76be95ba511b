import 'package:flutter/material.dart';
import '../services/fitness_service.dart';
import '../models/user.dart';
import '../models/workout_session.dart';
import '../widgets/shared/database_viewer.dart';

class DatabaseDemoScreen extends StatefulWidget {
  const DatabaseDemoScreen({super.key});

  @override
  State<DatabaseDemoScreen> createState() => _DatabaseDemoScreenState();
}

class _DatabaseDemoScreenState extends State<DatabaseDemoScreen> {
  final FitnessService _fitnessService = FitnessService();
  User? _currentUser;
  WorkoutSession? _currentSession;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Demo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.storage),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const DatabaseViewer()),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildUserSection(),
                  const SizedBox(height: 20),
                  _buildWorkoutSection(),
                  const SizedBox(height: 20),
                  _buildStatsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildUserSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'User Management',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            if (_currentUser != null) ...[
              Text('Current User: ${_currentUser!.name}'),
              Text('Email: ${_currentUser!.email}'),
              Text('Fitness Level: ${_currentUser!.fitnessLevel}'),
              const SizedBox(height: 10),
            ],
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _createDemoUser,
                    child: const Text('Create Demo User'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentUser != null ? _loadUser : null,
                    child: const Text('Load User'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Workout Session',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            if (_currentSession != null) ...[
              Text('Exercise: ${_currentSession!.exerciseType}'),
              Text('Started: ${_currentSession!.startTime.toString().split('.')[0]}'),
              if (_currentSession!.endTime != null) ...[
                Text('Duration: ${_currentSession!.duration}s'),
                Text('Accuracy: ${_currentSession!.avgAccuracy.toStringAsFixed(1)}%'),
              ],
              const SizedBox(height: 10),
            ],
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentUser != null && _currentSession == null 
                        ? _startWorkout 
                        : null,
                    child: const Text('Start Workout'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentSession != null && _currentSession!.endTime == null
                        ? _endWorkout 
                        : null,
                    child: const Text('End Workout'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _currentUser != null ? _addAnalysisResult : null,
                child: const Text('Add Analysis Result'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentUser != null ? _showUserStats : null,
                    child: const Text('Show Stats'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentUser != null ? _showWorkoutHistory : null,
                    child: const Text('Show History'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createDemoUser() async {
    setState(() => _isLoading = true);
    
    final user = await _fitnessService.createUser(
      name: 'Demo User ${DateTime.now().millisecondsSinceEpoch}',
      email: 'demo${DateTime.now().millisecondsSinceEpoch}@example.com',
      age: 25,
      height: 175.0,
      weight: 70.0,
      fitnessLevel: 'intermediate',
    );

    setState(() {
      _currentUser = user;
      _isLoading = false;
    });

    if (user != null) {
      _showSnackBar('Demo user created successfully!');
    } else {
      _showSnackBar('Failed to create demo user');
    }
  }

  Future<void> _loadUser() async {
    if (_currentUser?.email == null) return;
    
    setState(() => _isLoading = true);
    
    final user = await _fitnessService.getUserByEmail(_currentUser!.email);
    
    setState(() {
      _currentUser = user;
      _isLoading = false;
    });
  }

  Future<void> _startWorkout() async {
    if (_currentUser?.id == null) return;
    
    setState(() => _isLoading = true);
    
    final session = await _fitnessService.startWorkoutSession(
      userId: _currentUser!.id!,
      exerciseType: 'squat',
    );

    setState(() {
      _currentSession = session;
      _isLoading = false;
    });

    if (session != null) {
      _showSnackBar('Workout session started!');
    } else {
      _showSnackBar('Failed to start workout session');
    }
  }

  Future<void> _endWorkout() async {
    if (_currentSession?.id == null) return;
    
    setState(() => _isLoading = true);
    
    final session = await _fitnessService.endWorkoutSession(
      sessionId: _currentSession!.id!,
      totalReps: 20,
      correctReps: 15,
      incorrectReps: 5,
      notes: 'Demo workout session',
    );

    setState(() {
      _currentSession = session;
      _isLoading = false;
    });

    if (session != null) {
      _showSnackBar('Workout session ended! Accuracy: ${session.avgAccuracy.toStringAsFixed(1)}%');
    } else {
      _showSnackBar('Failed to end workout session');
    }
  }

  Future<void> _addAnalysisResult() async {
    if (_currentSession?.id == null) return;
    
    setState(() => _isLoading = true);
    
    final result = await _fitnessService.saveAnalysisResult(
      workoutSessionId: _currentSession!.id!,
      correctSquats: 15,
      incorrectSquats: 5,
      avgKneeAngle: 90.5,
      avgHipAngle: 85.2,
      feedback: ['Good form', 'Keep knees aligned', 'Maintain steady pace'],
    );

    setState(() => _isLoading = false);

    if (result != null) {
      _showSnackBar('Analysis result saved!');
    } else {
      _showSnackBar('Failed to save analysis result');
    }
  }

  Future<void> _showUserStats() async {
    if (_currentUser?.id == null) return;
    
    final stats = await _fitnessService.getUserStats(_currentUser!.id!);
    
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('User Statistics'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Total Workouts: ${stats['totalWorkouts']}'),
              Text('Total Time: ${stats['totalTime']}s'),
              Text('Average Accuracy: ${stats['avgAccuracy'].toStringAsFixed(1)}%'),
              Text('Total Correct Reps: ${stats['totalCorrectReps']}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _showWorkoutHistory() async {
    if (_currentUser?.id == null) return;
    
    final history = await _fitnessService.getUserWorkoutHistory(_currentUser!.id!);
    
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Workout History'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: history.isEmpty
                ? const Center(child: Text('No workout history found'))
                : ListView.builder(
                    itemCount: history.length,
                    itemBuilder: (context, index) {
                      final session = history[index];
                      return ListTile(
                        title: Text(session.exerciseType),
                        subtitle: Text(
                          'Reps: ${session.correctReps}/${session.totalReps} '
                          '(${session.avgAccuracy.toStringAsFixed(1)}%)',
                        ),
                        trailing: Text(
                          session.createdAt.toString().split(' ')[0],
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }
}
